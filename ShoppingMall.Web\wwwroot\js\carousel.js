let currentSlide = 0;
const slides = document.querySelectorAll('.carousel-slide');
const indicators = document.querySelectorAll('.carousel-indicator');
const totalSlides = slides.length;

function showSlide(n) {
    // 移除所有活动状态
    slides.forEach(slide => slide.classList.remove('active'));
    indicators.forEach(indicator => indicator.classList.remove('active'));
    
    // 设置当前幻灯片
    currentSlide = (n + totalSlides) % totalSlides;
    slides[currentSlide].classList.add('active');
    indicators[currentSlide].classList.add('active');
}

function nextSlide() {
    showSlide(currentSlide + 1);
}

function prevSlide() {
    showSlide(currentSlide - 1);
}

function goToSlide(n) {
    showSlide(n);
}

// 自动播放
let slideInterval = setInterval(nextSlide, 5000);

// 鼠标悬停时暂停自动播放
const carousel = document.querySelector('.carousel');
carousel.addEventListener('mouseenter', () => {
    clearInterval(slideInterval);
});

// 鼠标离开时恢复自动播放
carousel.addEventListener('mouseleave', () => {
    slideInterval = setInterval(nextSlide, 5000);
}); 