/* 购物车页面布局 */
.cart-page {
    padding: 20px 0;
}

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: var(--light-text);
}

.breadcrumb .current {
    color: var(--text-color);
}

/* 空购物车 */
.empty-cart {
    text-align: center;
    padding: 60px 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.empty-cart h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.empty-cart p {
    color: var(--light-text);
    margin-bottom: 20px;
}

.continue-shopping {
    display: inline-block;
    padding: 10px 30px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.continue-shopping:hover {
    background: #c53030;
}

/* 购物车内容 */
.cart-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 购物车表格 */
.cart-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 50px 3fr 1fr 1fr 1fr 80px;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
}

.select-all {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.select-all input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.header-item {
    text-align: center;
}

/* 购物车商品 */
.cart-item {
    display: grid;
    grid-template-columns: 50px 3fr 1fr 1fr 1fr 80px;
    padding: 20px 15px;
    border-bottom: 1px solid var(--border-color);
    align-items: center;
}

.item-select {
    display: flex;
    align-items: center;
}

.item-select input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.item-info {
    display: flex;
    gap: 15px;
}

.item-info img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.item-details {
    flex: 1;
}

.item-name {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-discount {
    display: inline-block;
    margin-top: 5px;
    padding: 2px 6px;
    background: #feebc8;
    color: #c05621;
    border-radius: 4px;
    font-size: 12px;
}

.item-price {
    text-align: center;
}

.current-price {
    color: var(--primary-color);
    font-weight: 500;
}

.original-price {
    display: block;
    color: var(--light-text);
    text-decoration: line-through;
    font-size: 12px;
    margin-top: 4px;
}

.item-quantity {
    text-align: center;
}

.quantity-controls {
    display: inline-flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.quantity-controls button {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: 14px;
}

.quantity-controls input {
    width: 50px;
    height: 32px;
    border: none;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    text-align: center;
    font-size: 14px;
}

.stock-info {
    color: var(--light-text);
    font-size: 12px;
    margin-top: 4px;
}

.item-subtotal {
    text-align: center;
    color: var(--primary-color);
    font-weight: 500;
}

.item-actions {
    text-align: center;
}

.remove-btn {
    border: none;
    background: none;
    color: var(--light-text);
    cursor: pointer;
    transition: color 0.2s;
}

.remove-btn:hover {
    color: var(--primary-color);
}

/* 购物车底部 */
.cart-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.footer-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.clear-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--light-text);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.clear-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.selected-count {
    color: var(--light-text);
}

.footer-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.total-price {
    font-size: 16px;
}

.total-price span {
    color: var(--primary-color);
    font-size: 24px;
    font-weight: bold;
}

.checkout-btn {
    padding: 12px 40px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.checkout-btn:hover {
    background: #c53030;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-header {
        display: none;
    }

    .cart-item {
        grid-template-columns: 50px 1fr;
        grid-template-areas:
            "select info"
            "select price"
            "select quantity"
            "select subtotal"
            "select actions";
        gap: 10px;
    }

    .item-select {
        grid-area: select;
    }

    .item-info {
        grid-area: info;
    }

    .item-price {
        grid-area: price;
        text-align: left;
    }

    .item-quantity {
        grid-area: quantity;
        text-align: left;
    }

    .item-subtotal {
        grid-area: subtotal;
        text-align: left;
    }

    .item-actions {
        grid-area: actions;
        text-align: left;
    }

    .cart-footer {
        flex-direction: column;
        gap: 20px;
    }

    .footer-left,
    .footer-right {
        width: 100%;
        justify-content: space-between;
    }
} 