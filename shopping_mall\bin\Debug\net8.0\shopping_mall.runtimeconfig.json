{"runtimeOptions": {"tfm": "net8.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "8.0.0"}, {"name": "Microsoft.AspNetCore.App", "version": "8.0.0"}], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.Diagnostics.Tracing.EventSource.IsSupported": true, "System.GC.Server": true, "System.GC.DynamicAdaptationMode": 1, "System.Globalization.Invariant": true, "System.Globalization.PredefinedCulturesOnly": true, "System.Resources.ResourceManager.AllowCustomResourceTypes": false, "System.Runtime.CompilerServices.RuntimeFeature.IsDynamicCodeSupported": false, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": false, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInterop": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault": false, "System.Threading.Thread.EnableAutoreleasePool": false, "System.Linq.Expressions.CanEmitObjectArrayDelegate": false, "Microsoft.AspNetCore.SignalR.Hub.IsCustomAwaitableSupported": false, "Microsoft.AspNetCore.Mvc.ApiExplorer.IsEnhancedModelMetadataSupported": false}}}