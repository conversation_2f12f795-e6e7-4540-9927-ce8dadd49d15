/* 商品页面布局 */
.product-page {
    padding: 20px 0;
}

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: var(--light-text);
}

.breadcrumb .current {
    color: var(--text-color);
}

/* 商品网格布局 */
.product-grid {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
}

/* 左侧筛选栏 */
.filter-sidebar {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-section {
    margin-bottom: 30px;
}

.filter-section h3 {
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--text-color);
}

.filter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-list li {
    margin-bottom: 10px;
}

.filter-list a {
    color: var(--light-text);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.filter-list a:hover,
.filter-list a.active {
    color: var(--primary-color);
}

.price-filter {
    margin-top: 15px;
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.price-inputs input {
    width: 100px;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

.filter-btn {
    width: 100%;
    padding: 8px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.filter-btn:hover {
    background: #c53030;
}

/* 右侧商品内容 */
.product-content {
    min-height: 800px;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sort-options {
    display: flex;
    gap: 15px;
}

.sort-option {
    color: var(--light-text);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.sort-option:hover,
.sort-option.active {
    color: var(--primary-color);
}

.view-options {
    display: flex;
    gap: 10px;
}

.view-btn {
    background: none;
    border: none;
    color: var(--light-text);
    cursor: pointer;
    padding: 5px;
    transition: color 0.2s;
}

.view-btn:hover,
.view-btn.active {
    color: var(--primary-color);
}

/* 商品卡片 - 网格视图 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.product-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    padding-top: 100%;
}

.product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.product-info {
    padding: 15px;
}

.product-name {
    font-size: 16px;
    margin-bottom: 10px;
}

.product-name a {
    color: var(--text-color);
    text-decoration: none;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
}

.product-brand {
    color: var(--light-text);
}

.product-rating {
    color: #f6ad55;
}

.rating-count {
    color: var(--light-text);
    font-size: 12px;
}

.product-price {
    margin-bottom: 15px;
}

.current-price {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
}

.original-price {
    margin-left: 8px;
    font-size: 14px;
    color: var(--light-text);
    text-decoration: line-through;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.add-to-cart {
    flex: 1;
    padding: 8px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.add-to-cart:hover {
    background: #c53030;
}

.add-to-wishlist {
    padding: 8px;
    background: white;
    color: var(--light-text);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.add-to-wishlist:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 商品卡片 - 列表视图 */
.products-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.products-list .product-card {
    display: grid;
    grid-template-columns: 200px 1fr;
}

.products-list .product-image {
    padding-top: 0;
    height: 200px;
}

.products-list .product-info {
    display: grid;
    grid-template-rows: auto 1fr auto;
}

.products-list .product-name {
    font-size: 18px;
}

.products-list .product-meta {
    margin: 10px 0;
}

.products-list .product-actions {
    align-self: end;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 30px;
}

.page-link {
    padding: 8px 12px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s;
}

.page-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-link.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: 200px 1fr;
    }
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: 1fr;
    }

    .filter-sidebar {
        display: none;
    }

    .products-list .product-card {
        grid-template-columns: 150px 1fr;
    }

    .products-list .product-image {
        height: 150px;
    }
} 