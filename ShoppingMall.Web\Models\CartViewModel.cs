namespace ShoppingMall.Web.Models;

public class CartViewModel
{
    public List<CartItem> Items { get; set; } = new();
    public decimal TotalPrice => Items.Where(i => i.IsSelected).Sum(i => i.SubTotal);
    public int TotalQuantity => Items.Where(i => i.IsSelected).Sum(i => i.Quantity);
    public bool AllSelected => Items.Any() && Items.All(i => i.IsSelected);

    // 添加缺少的属性
    public int SelectedCount => Items.Where(i => i.IsSelected).Sum(i => i.Quantity);
    public decimal Total => TotalPrice;
}