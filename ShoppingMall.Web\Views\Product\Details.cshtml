@model ShoppingMall.Web.Models.ProductDetailsViewModel
@{
    ViewData["Title"] = Model.Product.Name;
}

<link rel="stylesheet" href="~/css/product-details.css" />

<div class="product-details">
    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">首页</a>
            <span class="separator">/</span>
            <a href="@Url.Action("Index", new { category = Model.Product.Category })">@Model.Product.Category</a>
            <span class="separator">/</span>
            <span class="current">@Model.Product.Name</span>
        </div>

        <!-- 商品信息 -->
        <div class="product-info">
            <!-- 左侧图片 -->
            <div class="product-gallery">
                <div class="main-image">
                    <img src="@Model.Product.ImageUrl" alt="@Model.Product.Name" id="mainImage" />
                    @if (Model.Product.HasDiscount)
                    {
                        <span class="discount-badge">-@Model.Product.DiscountPercentage%</span>
                    }
                </div>
                @if (Model.Product.AdditionalImages.Any())
                {
                    <div class="thumbnail-list">
                        <button class="thumb-nav prev" onclick="scrollThumbnails(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="thumbnails">
                            <img src="@Model.Product.ImageUrl" alt="@Model.Product.Name" 
                                 class="thumbnail active" onclick="changeMainImage(this)" />
                            @foreach (var image in Model.Product.AdditionalImages)
                            {
                                <img src="@image" alt="@Model.Product.Name" 
                                     class="thumbnail" onclick="changeMainImage(this)" />
                            }
                        </div>
                        <button class="thumb-nav next" onclick="scrollThumbnails(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                }
            </div>

            <!-- 右侧信息 -->
            <div class="product-details-info">
                <h1 class="product-title">@Model.Product.Name</h1>
                
                <div class="product-meta">
                    <div class="rating">
                        <div class="stars">
                            @for (var i = 1; i <= 5; i++)
                            {
                                if (i <= Math.Floor(Model.AverageRating))
                                {
                                    <i class="fas fa-star"></i>
                                }
                                else if (i - Model.AverageRating < 1 && i - Model.AverageRating > 0)
                                {
                                    <i class="fas fa-star-half-alt"></i>
                                }
                                else
                                {
                                    <i class="far fa-star"></i>
                                }
                            }
                        </div>
                        <span class="rating-value">@Model.AverageRating.ToString("F1")</span>
                        <a href="#reviews" class="review-count">(@Model.ReviewCount 条评价)</a>
                    </div>
                    <div class="brand">品牌：@Model.Product.Brand</div>
                </div>

                <div class="product-price">
                    <div class="current-price">¥@Model.Product.Price.ToString("F2")</div>
                    @if (Model.Product.HasDiscount)
                    {
                        <div class="original-price">¥@Model.Product.OriginalPrice?.ToString("F2")</div>
                        <div class="discount">@Model.Product.DiscountPercentage%折扣</div>
                    }
                </div>

                <div class="product-options">
                    <div class="quantity-selector">
                        <label>数量：</label>
                        <div class="quantity-controls">
                            <button onclick="updateQuantity(-1)">-</button>
                            <input type="number" id="quantity" value="1" min="1" max="@Model.Product.Stock" />
                            <button onclick="updateQuantity(1)">+</button>
                        </div>
                        <span class="stock">库存：@Model.Product.Stock</span>
                    </div>
                </div>

                <div class="product-actions">
                    <button class="add-to-cart" onclick="addToCart(@Model.Product.Id)">
                        <i class="fas fa-shopping-cart"></i>
                        加入购物车
                    </button>
                    <button class="buy-now" onclick="buyNow(@Model.Product.Id)">
                        立即购买
                    </button>
                </div>

                <div class="product-services">
                    <div class="service-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>正品保证</span>
                    </div>
                    <div class="service-item">
                        <i class="fas fa-truck"></i>
                        <span>极速发货</span>
                    </div>
                    <div class="service-item">
                        <i class="fas fa-undo"></i>
                        <span>7天无理由退换</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商品详情和评价 -->
        <div class="product-tabs">
            <div class="tab-header">
                <button class="tab-btn active" onclick="switchTab(this, 'details')">商品详情</button>
                <button class="tab-btn" onclick="switchTab(this, 'specs')">规格参数</button>
                <button class="tab-btn" onclick="switchTab(this, 'reviews')">
                    商品评价<span class="review-count">(@Model.ReviewCount)</span>
                </button>
            </div>

            <div class="tab-content">
                <!-- 商品详情 -->
                <div class="tab-pane active" id="details">
                    <div class="product-description">
                        @Html.Raw(Model.Product.Description)
                    </div>
                </div>

                <!-- 规格参数 -->
                <div class="tab-pane" id="specs">
                    <div class="specifications">
                        @Html.Raw(Model.Product.Specifications)
                    </div>
                </div>

                <!-- 商品评价 -->
                <div class="tab-pane" id="reviews">
                    <div class="review-summary">
                        <div class="rating-overview">
                            <div class="average-rating">
                                <div class="rating-number">@Model.AverageRating.ToString("F1")</div>
                                <div class="rating-stars">
                                    @for (var i = 1; i <= 5; i++)
                                    {
                                        if (i <= Math.Floor(Model.AverageRating))
                                        {
                                            <i class="fas fa-star"></i>
                                        }
                                        else if (i - Model.AverageRating < 1 && i - Model.AverageRating > 0)
                                        {
                                            <i class="fas fa-star-half-alt"></i>
                                        }
                                        else
                                        {
                                            <i class="far fa-star"></i>
                                        }
                                    }
                                </div>
                                <div class="total-reviews">共 @Model.ReviewCount 条评价</div>
                            </div>
                        </div>
                    </div>

                    <div class="review-list">
                        @foreach (var review in Model.Reviews)
                        {
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <img src="/images/avatar.png" alt="@review.UserName" class="reviewer-avatar" />
                                        <span class="reviewer-name">@review.UserName</span>
                                    </div>
                                    <div class="review-rating">
                                        @for (var i = 1; i <= 5; i++)
                                        {
                                            <i class="fas fa-star @(i <= review.Rating ? "active" : "")"></i>
                                        }
                                    </div>
                                </div>
                                <div class="review-content">@review.Content</div>
                                @if (review.Images.Any())
                                {
                                    <div class="review-images">
                                        @foreach (var image in review.Images)
                                        {
                                            <img src="@image" alt="评价图片" onclick="showImage(this.src)" />
                                        }
                                    </div>
                                }
                                <div class="review-footer">
                                    <div class="review-date">@review.CreatedAt.ToString("yyyy-MM-dd")</div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- 相关商品 -->
        @if (Model.RelatedProducts.Any())
        {
            <div class="related-products">
                <h2>相关商品</h2>
                <div class="products-grid">
                    @foreach (var product in Model.RelatedProducts)
                    {
                        <div class="product-card">
                            <div class="product-image">
                                @if (product.HasDiscount)
                                {
                                    <span class="discount-badge">-@product.DiscountPercentage%</span>
                                }
                                <a href="@Url.Action("Details", new { id = product.Id })">
                                    <img src="@product.ImageUrl" alt="@product.Name" loading="lazy" />
                                </a>
                            </div>
                            <div class="product-info">
                                <h3 class="product-name">
                                    <a href="@Url.Action("Details", new { id = product.Id })">@product.Name</a>
                                </h3>
                                <div class="product-price">
                                    <span class="current-price">¥@product.Price.ToString("F2")</span>
                                    @if (product.HasDiscount)
                                    {
                                        <span class="original-price">¥@product.OriginalPrice?.ToString("F2")</span>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function updateQuantity(delta) {
            const input = document.getElementById('quantity');
            const newValue = parseInt(input.value) + delta;
            if (newValue >= parseInt(input.min) && newValue <= parseInt(input.max)) {
                input.value = newValue;
            }
        }

        function addToCart(productId) {
            const quantity = parseInt(document.getElementById('quantity').value);
            fetch('/Cart/Add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify({ productId, quantity })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('已添加到购物车');
                }
            });
        }

        function buyNow(productId) {
            const quantity = parseInt(document.getElementById('quantity').value);
            addToCart(productId);
            location.href = '/Cart';
        }

        function changeMainImage(thumbnail) {
            document.getElementById('mainImage').src = thumbnail.src;
            document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
            thumbnail.classList.add('active');
        }

        function scrollThumbnails(direction) {
            const container = document.querySelector('.thumbnails');
            container.scrollLeft += direction * 100;
        }

        function switchTab(button, tabId) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        function showImage(src) {
            // 实现图片预览功能
        }
    </script>
} 