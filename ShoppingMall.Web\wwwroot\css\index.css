/* 主内容布局 */
.main-content {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: 20px;
    margin-top: 20px;
}

/* 侧边栏样式 */
.sidebar {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.category-menu {
    padding: 15px;
}

.category-menu h3 {
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.category-list {
    list-style: none;
}

.category-list li {
    margin-bottom: 10px;
}

.category-list a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.category-list a:hover {
    background: var(--background-color);
}

.subcategories {
    font-size: 12px;
    color: var(--light-text);
}

/* 轮播图样式 */
.hero-section {
    margin-bottom: 30px;
}

.carousel {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    height: 400px;
}

.carousel-slide {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.carousel-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    border: none;
    cursor: pointer;
}

.carousel-indicator.active {
    background: white;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.3);
    color: white;
    border: none;
    padding: 20px 15px;
    cursor: pointer;
    font-size: 24px;
    transition: background-color 0.2s;
}

.carousel-nav:hover {
    background: rgba(0,0,0,0.5);
}

.carousel-prev {
    left: 0;
    border-radius: 0 4px 4px 0;
}

.carousel-next {
    right: 0;
    border-radius: 4px 0 0 4px;
}

/* 产品区域样式 */
.products-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    font-size: 20px;
    color: var(--text-color);
}

.section-more {
    color: var(--light-text);
    text-decoration: none;
    font-size: 14px;
}

.section-more:hover {
    color: var(--primary-color);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.product-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.product-image {
    position: relative;
    padding-top: 100%;
}

.product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.product-info {
    padding: 15px;
}

.product-title {
    font-size: 14px;
    margin-bottom: 10px;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    margin-bottom: 10px;
}

.current-price {
    color: var(--primary-color);
    font-size: 18px;
    font-weight: bold;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.add-to-cart {
    flex: 1;
    padding: 8px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.add-to-cart:hover {
    background: #c53030;
}

.add-to-wishlist {
    padding: 8px;
    background: white;
    color: var(--light-text);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.add-to-wishlist:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .carousel {
        height: 300px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
} 