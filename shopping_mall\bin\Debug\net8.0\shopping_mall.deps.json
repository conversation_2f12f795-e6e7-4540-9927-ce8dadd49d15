{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"shopping_mall/1.0.0": {"dependencies": {"Microsoft.DotNet.ILCompiler": "8.0.15", "Microsoft.NET.ILLink.Tasks": "8.0.15"}, "runtime": {"shopping_mall.dll": {}}}, "Microsoft.DotNet.ILCompiler/8.0.15": {}, "Microsoft.NET.ILLink.Tasks/8.0.15": {}}}, "libraries": {"shopping_mall/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.DotNet.ILCompiler/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-wMf2N7fJ846aKd73R5gqvtbyqu89/LywlWCtMyXUqKYc9DR3s9kUgNrLIsT9KeRwyinGFJDtRbiib0M4YBX6ZA==", "path": "microsoft.dotnet.ilcompiler/8.0.15", "hashPath": "microsoft.dotnet.ilcompiler.8.0.15.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-s4eXlcRGyHeCgFUGQnhq0e/SCHBPp0jOHgMqZg3fQ2OCHJSm1aOUhI6RFWuVIcEb9ig2WgI2kWukk8wu72EbUQ==", "path": "microsoft.net.illink.tasks/8.0.15", "hashPath": "microsoft.net.illink.tasks.8.0.15.nupkg.sha512"}}}