namespace ShoppingMall.Web.Models;

public class OrderListViewModel
{
    public List<Order> Orders { get; set; } = new();
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int TotalItems { get; set; }
    public string? Status { get; set; }
    
    public int TotalPages => (int)Math.Ceiling(TotalItems / (double)PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
    
    public Dictionary<string, string> StatusOptions => new()
    {
        { "待付款", "待付款" },
        { "待发货", "待发货" },
        { "已发货", "已发货" },
        { "已完成", "已完成" },
        { "已取消", "已取消" }
    };
} 