@model ShoppingMall.Web.Models.Product

@{
    ViewData["Title"] = "添加商品";
}

<h1>@ViewData["Title"]</h1>

<div class="row">
    <div class="col-md-8">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            
            <div class="form-group mb-3">
                <label asp-for="Name" class="control-label">商品名称</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            
            <div class="form-group mb-3">
                <label asp-for="Description" class="control-label">商品描述</label>
                <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            
            <div class="form-group mb-3">
                <label asp-for="Price" class="control-label">价格</label>
                <input asp-for="Price" class="form-control" type="number" step="0.01" />
                <span asp-validation-for="Price" class="text-danger"></span>
            </div>
            
            <div class="form-group mb-3">
                <label asp-for="ImageUrl" class="control-label">图片URL</label>
                <input asp-for="ImageUrl" class="form-control" />
                <span asp-validation-for="ImageUrl" class="text-danger"></span>
            </div>
            
            <div class="form-group mb-3">
                <label asp-for="Stock" class="control-label">库存数量</label>
                <input asp-for="Stock" class="form-control" type="number" />
                <span asp-validation-for="Stock" class="text-danger"></span>
            </div>
            
            <div class="form-group mb-3">
                <div class="form-check">
                    <input asp-for="IsAvailable" class="form-check-input" />
                    <label asp-for="IsAvailable" class="form-check-label">是否上架</label>
                </div>
            </div>
            
            <div class="form-group">
                <input type="submit" value="创建" class="btn btn-primary" />
                <a asp-action="Index" class="btn btn-secondary">返回列表</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 