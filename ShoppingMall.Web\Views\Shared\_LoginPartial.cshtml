@using Microsoft.AspNetCore.Identity

@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

<div class="nav-user">
    @if (SignInManager.IsSignedIn(User))
    {
        <div class="user-menu">
            <button class="user-menu-btn">
                <i class="fas fa-user-circle"></i>
                <span>@User.Identity?.Name</span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="user-menu-dropdown">
                <a asp-controller="Account" asp-action="Profile" class="user-menu-item">
                    <i class="fas fa-user"></i>
                    <span>个人中心</span>
                </a>
                <a asp-controller="Order" asp-action="Index" class="user-menu-item">
                    <i class="fas fa-shopping-bag"></i>
                    <span>我的订单</span>
                </a>
                <a asp-controller="Account" asp-action="Settings" class="user-menu-item">
                    <i class="fas fa-cog"></i>
                    <span>账户设置</span>
                </a>
                <form asp-controller="Account" asp-action="Logout" method="post">
                    <button type="submit" class="user-menu-item">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </button>
                </form>
            </div>
        </div>
    }
    else
    {
        <div class="nav-auth">
            <a asp-controller="Account" asp-action="Login" class="nav-link">登录</a>
            <a asp-controller="Account" asp-action="Register" class="nav-btn">注册</a>
        </div>
    }
</div> 