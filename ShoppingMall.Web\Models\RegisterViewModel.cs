using System.ComponentModel.DataAnnotations;

namespace ShoppingMall.Web.Models;

public class RegisterViewModel
{
    [Required(ErrorMessage = "请输入邮箱")]
    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    public string Email { get; set; } = null!;

    [Required(ErrorMessage = "请输入密码")]
    [StringLength(100, ErrorMessage = "{0}必须至少包含{2}个字符。", MinimumLength = 6)]
    [DataType(DataType.Password)]
    public string Password { get; set; } = null!;

    [DataType(DataType.Password)]
    [Compare("Password", ErrorMessage = "密码和确认密码不匹配。")]
    public string ConfirmPassword { get; set; } = null!;
} 