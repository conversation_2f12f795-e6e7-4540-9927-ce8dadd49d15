@model ShoppingMall.Web.Models.ProductListViewModel
@{
    ViewData["Title"] = "商品列表";
}

<link rel="stylesheet" href="~/css/product-list.css" />

<div class="product-page">
    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">首页</a>
            <span class="separator">/</span>
            <span class="current">@(Model.Category ?? "全部商品")</span>
        </div>

        <div class="product-grid">
            <!-- 右侧商品列表 -->
            <div class="product-content">
                <!-- 排序和筛选工具栏 -->
                <div class="toolbar">
                    <div class="sort-options">
                        <a href="@Url.Action("Index", new { sortBy = "price_asc" })" 
                           class="sort-option @("price_asc" == Model.SortBy ? "active" : "")">
                            价格从低到高
                        </a>
                        <a href="@Url.Action("Index", new { sortBy = "price_desc" })" 
                           class="sort-option @("price_desc" == Model.SortBy ? "active" : "")">
                            价格从高到低
                        </a>
                        <a href="@Url.Action("Index", new { sortBy = "newest" })" 
                           class="sort-option @("newest" == Model.SortBy ? "active" : "")">
                            最新上架
                        </a>
                    </div>
                    <div class="view-options">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="products-grid">
                    @foreach (var product in Model.Products)
                    {
                        <div class="product-card">
                            <div class="product-image">
                                @if (product.HasDiscount)
                                {
                                    <span class="discount-badge">-@product.DiscountPercentage%</span>
                                }
                                <a href="@Url.Action("Details", new { id = product.Id })">
                                    <img src="@product.ImageUrl" alt="@product.Name" loading="lazy" />
                                </a>
                            </div>
                            <div class="product-info">
                                <h3 class="product-name">
                                    <a href="@Url.Action("Details", new { id = product.Id })">@product.Name</a>
                                </h3>
                                <div class="product-meta">
                                    <span class="product-brand">@product.Brand</span>
                                </div>
                                <div class="product-price">
                                    <span class="current-price">¥@product.Price.ToString("F2")</span>
                                    @if (product.HasDiscount)
                                    {
                                        <span class="original-price">¥@product.OriginalPrice?.ToString("F2")</span>
                                    }
                                </div>
                                <div class="product-actions">
                                    <button class="add-to-cart" onclick="addToCart(@product.Id)">
                                        <i class="fas fa-shopping-cart"></i>
                                        加入购物车
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- 分页 -->
                @if (Model.TotalPages > 1)
                {
                    <div class="pagination">
                        @if (Model.HasPreviousPage)
                        {
                            <a href="@Url.Action("Index", new { 
                                   page = Model.PageNumber - 1,
                                   category = Model.Category,
                                   brand = Model.Brand,
                                   sortBy = Model.SortBy
                               })" 
                               class="page-link">
                                上一页
                            </a>
                        }

                        @for (var i = 1; i <= Model.TotalPages; i++)
                        {
                            if (i == Model.PageNumber)
                            {
                                <span class="page-link active">@i</span>
                            }
                            else
                            {
                                <a href="@Url.Action("Index", new { 
                                       page = i,
                                       category = Model.Category,
                                       brand = Model.Brand,
                                       sortBy = Model.SortBy
                                   })" 
                                   class="page-link">
                                    @i
                                </a>
                            }
                        }

                        @if (Model.HasNextPage)
                        {
                            <a href="@Url.Action("Index", new { 
                                   page = Model.PageNumber + 1,
                                   category = Model.Category,
                                   brand = Model.Brand,
                                   sortBy = Model.SortBy
                               })" 
                               class="page-link">
                                下一页
                            </a>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function addToCart(productId) {
            fetch('/Cart/Add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify({ productId, quantity: 1 })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('已添加到购物车');
                }
            });
        }

        // 切换视图模式
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const view = btn.dataset.view;
                const productsContainer = document.querySelector('.products-grid');
                productsContainer.className = `products-${view}`;
            });
        });
    </script>
} 