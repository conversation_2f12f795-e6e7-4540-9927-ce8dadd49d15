namespace ShoppingMall.Web.Models;

public class CartItem
{
    public int Id { get; set; }
    public string UserId { get; set; } = null!;
    public int ProductId { get; set; }
    public Product Product { get; set; } = null!;
    public int Quantity { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsSelected { get; set; }
    
    public decimal SubTotal => Product.Price * Quantity;
} 