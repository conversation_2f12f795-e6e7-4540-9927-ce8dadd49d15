using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ShoppingMall.Web.Data;
using ShoppingMall.Web.Models;

namespace ShoppingMall.Web.Controllers;

[Authorize]
public class OrderController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<IdentityUser> _userManager;
    private const int PageSize = 10;

    public OrderController(
        ApplicationDbContext context,
        UserManager<IdentityUser> userManager)
    {
        _context = context;
        _userManager = userManager;
    }

    public async Task<IActionResult> Index(string? status = null, int page = 1)
    {
        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var query = _context.Orders
            .Include(o => o.Items)
            .Where(o => o.UserId == userId);

        if (!string.IsNullOrEmpty(status))
        {
            query = query.Where(o => o.Status == status);
        }

        var totalItems = await query.CountAsync();
        var orders = await query
            .OrderByDescending(o => o.CreatedAt)
            .Skip((page - 1) * PageSize)
            .Take(PageSize)
            .ToListAsync();

        var viewModel = new OrderListViewModel
        {
            Orders = orders,
            PageNumber = page,
            PageSize = PageSize,
            TotalItems = totalItems,
            Status = status
        };

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var order = await _context.Orders
            .Include(o => o.Items)
            .ThenInclude(i => i.Product)
            .FirstOrDefaultAsync(o => o.Id == id && o.UserId == userId);

        if (order == null)
            return NotFound();

        return View(order);
    }

    public async Task<IActionResult> Checkout()
    {
        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var cartItems = await _context.CartItems
            .Include(c => c.Product)
            .Where(c => c.UserId == userId && c.IsSelected)
            .ToListAsync();

        if (!cartItems.Any())
            return RedirectToAction("Index", "Cart");

        var viewModel = new CheckoutViewModel { Items = cartItems };
        return View(viewModel);
    }

    [HttpPost]
    public async Task<IActionResult> Checkout(CheckoutViewModel model)
    {
        if (!ModelState.IsValid)
            return View(model);

        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
            return Challenge();

        var cartItems = await _context.CartItems
            .Include(c => c.Product)
            .Where(c => c.UserId == userId && c.IsSelected)
            .ToListAsync();

        if (!cartItems.Any())
            return RedirectToAction("Index", "Cart");

        // 创建订单
        var order = new Order
        {
            UserId = userId,
            UserName = user.UserName ?? "Unknown",
            ReceiverName = model.ReceiverName,
            ReceiverPhone = model.ReceiverPhone,
            ReceiverAddress = model.ReceiverAddress,
            TotalAmount = cartItems.Sum(i => i.SubTotal),
            Status = "待付款",
            CreatedAt = DateTime.UtcNow,
            Items = cartItems.Select(i => new OrderItem
            {
                ProductId = i.ProductId,
                Price = i.Product.Price,
                Quantity = i.Quantity
            }).ToList()
        };

        _context.Orders.Add(order);

        // 清除已选择的购物车商品
        _context.CartItems.RemoveRange(cartItems);

        await _context.SaveChangesAsync();

        return RedirectToAction(nameof(Details), new { id = order.Id });
    }

    [HttpPost]
    public async Task<IActionResult> Cancel(int id, string reason)
    {
        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var order = await _context.Orders
            .FirstOrDefaultAsync(o => o.Id == id && o.UserId == userId);

        if (order == null)
            return NotFound();

        if (order.Status != "待付款")
            return BadRequest("只能取消待付款的订单");

        order.Status = "已取消";
        order.CancelledAt = DateTime.UtcNow;
        order.CancelReason = reason;

        await _context.SaveChangesAsync();
        return Json(new { success = true });
    }

    [HttpPost]
    public async Task<IActionResult> Pay(int id)
    {
        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var order = await _context.Orders
            .FirstOrDefaultAsync(o => o.Id == id && o.UserId == userId);

        if (order == null)
            return NotFound();

        if (order.Status != "待付款")
            return BadRequest("订单状态错误");

        // TODO: 实现支付功能
        order.Status = "待发货";
        order.PaidAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return Json(new { success = true });
    }

    [HttpPost]
    public async Task<IActionResult> Confirm(int id)
    {
        var userId = _userManager.GetUserId(User);
        if (userId == null)
            return Challenge();

        var order = await _context.Orders
            .FirstOrDefaultAsync(o => o.Id == id && o.UserId == userId);

        if (order == null)
            return NotFound();

        if (order.Status != "已发货")
            return BadRequest("订单状态错误");

        order.Status = "已完成";
        order.CompletedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return Json(new { success = true });
    }
} 