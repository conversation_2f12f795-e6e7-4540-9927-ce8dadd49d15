{"Version": 1, "Hash": "U6HQFarvpr9zbmsPBmFXnMNeSFi+CwIIs0J4TsV4UDU=", "Source": "ShoppingMall.Web", "BasePath": "_content/ShoppingMall.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ShoppingMall.Web\\wwwroot", "Source": "ShoppingMall.Web", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "Pattern": "**"}], "Assets": [{"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\auth.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/auth#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "54fhv3lqs3", "Integrity": "xaFdP3NsEcd4g9uyZRcd9dzcHWuBzPueCi8xeE/637Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\auth.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\cart.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/cart#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qhsnkupta7", "Integrity": "AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\cart.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\common.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/common#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v73v39fes", "Integrity": "kAVobVExTrhO86RUSP3qOhEQ/OVrFvVIMLyJX1hMmGE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\common.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\index.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/index#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j7cy4uu<PERSON>j", "Integrity": "T84E6e6zHndu/nSusbbs/eCsnR2jZ8M3VeVOQMy8M9s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\index.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-details.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/product-details#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c84xq4nezm", "Integrity": "VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\product-details.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-list.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/product-list#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9tdl9fs3uh", "Integrity": "Lf7XBRmQ3pBB3ekBh09/zEO9k3jiYzf3wVoYrsoz2mU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\product-list.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051415340001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051415340001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qokh4uiflp", "Integrity": "BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051415340001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416060001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051416060001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hw1ap2h7wz", "Integrity": "3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051416060001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051416070001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8co0co73td", "Integrity": "OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051416070001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070002.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051416070002#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t66wtv88gg", "Integrity": "VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051416070002.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051717070001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051717070001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m0xi4xxp31", "Integrity": "WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051717070001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051800550001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051800550001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6i8a2md2c7", "Integrity": "cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051800550001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801210001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051801210001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tx7hrwy3ub", "Integrity": "cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051801210001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801300001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051801300001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mcac68kan4", "Integrity": "ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051801300001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801390001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051801390001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tvh7n3sxrd", "Integrity": "SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051801390001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801470001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051801470001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1j99jfbvzg", "Integrity": "g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051801470001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801580001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051801580001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xdjj6mqi2s", "Integrity": "5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051801580001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051802100001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015051802100001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e1qt27ox10", "Integrity": "7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015051802100001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301410001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015052301410001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9xalzd3pbe", "Integrity": "cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015052301410001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301500001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015052301500001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vcnlt9rpyp", "Integrity": "gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015052301500001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301590001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015052301590001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u6l54bdsf3", "Integrity": "hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015052301590001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052302050001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015052302050001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tfepd08b58", "Integrity": "p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015052302050001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052501290001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/bxite-G2015052501290001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kcc1gx74e1", "Integrity": "xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\bxite-G2015052501290001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\carousel.js", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "js/carousel#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4td0zeg1u6", "Integrity": "GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\carousel.js"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\common.js", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "js/common#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dsj294zl38", "Integrity": "U4ZP3IWG+V+aKfGyBgAQsGaxIrgUQ5LMpupFVL6E0zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\common.js"}], "Endpoints": [{"Route": "css/auth.54fhv3lqs3.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4761"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xaFdP3NsEcd4g9uyZRcd9dzcHWuBzPueCi8xeE/637Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "54fhv3lqs3"}, {"Name": "label", "Value": "css/auth.css"}, {"Name": "integrity", "Value": "sha256-xaFdP3NsEcd4g9uyZRcd9dzcHWuBzPueCi8xeE/637Q="}]}, {"Route": "css/auth.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4761"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xaFdP3NsEcd4g9uyZRcd9dzcHWuBzPueCi8xeE/637Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xaFdP3NsEcd4g9uyZRcd9dzcHWuBzPueCi8xeE/637Q="}]}, {"Route": "css/cart.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs="}]}, {"Route": "css/cart.qhsnkupta7.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qhsnkupta7"}, {"Name": "label", "Value": "css/cart.css"}, {"Name": "integrity", "Value": "sha256-AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs="}]}, {"Route": "css/common.4v73v39fes.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kAVobVExTrhO86RUSP3qOhEQ/OVrFvVIMLyJX1hMmGE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 17:03:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v73v39fes"}, {"Name": "label", "Value": "css/common.css"}, {"Name": "integrity", "Value": "sha256-kAVobVExTrhO86RUSP3qOhEQ/OVrFvVIMLyJX1hMmGE="}]}, {"Route": "css/common.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kAVobVExTrhO86RUSP3qOhEQ/OVrFvVIMLyJX1hMmGE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 17:03:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kAVobVExTrhO86RUSP3qOhEQ/OVrFvVIMLyJX1hMmGE="}]}, {"Route": "css/index.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\index.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5013"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T84E6e6zHndu/nSusbbs/eCsnR2jZ8M3VeVOQMy8M9s=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 17:04:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T84E6e6zHndu/nSusbbs/eCsnR2jZ8M3VeVOQMy8M9s="}]}, {"Route": "css/index.j7cy4uujoj.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\index.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5013"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T84E6e6zHndu/nSusbbs/eCsnR2jZ8M3VeVOQMy8M9s=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 17:04:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j7cy4uu<PERSON>j"}, {"Name": "label", "Value": "css/index.css"}, {"Name": "integrity", "Value": "sha256-T84E6e6zHndu/nSusbbs/eCsnR2jZ8M3VeVOQMy8M9s="}]}, {"Route": "css/product-details.c84xq4nezm.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-details.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c84xq4nezm"}, {"Name": "label", "Value": "css/product-details.css"}, {"Name": "integrity", "Value": "sha256-VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA="}]}, {"Route": "css/product-details.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-details.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA="}]}, {"Route": "css/product-list.9tdl9fs3uh.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6674"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Lf7XBRmQ3pBB3ekBh09/zEO9k3jiYzf3wVoYrsoz2mU=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9tdl9fs3uh"}, {"Name": "label", "Value": "css/product-list.css"}, {"Name": "integrity", "Value": "sha256-Lf7XBRmQ3pBB3ekBh09/zEO9k3jiYzf3wVoYrsoz2mU="}]}, {"Route": "css/product-list.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6674"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Lf7XBRmQ3pBB3ekBh09/zEO9k3jiYzf3wVoYrsoz2mU=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Lf7XBRmQ3pBB3ekBh09/zEO9k3jiYzf3wVoYrsoz2mU="}]}, {"Route": "images/banners/bxite-F2015051415340001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051415340001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "169333"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk="}]}, {"Route": "images/banners/bxite-F2015051415340001.qokh4uiflp.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051415340001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "169333"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qokh4uiflp"}, {"Name": "label", "Value": "images/banners/bxite-F2015051415340001.jpg"}, {"Name": "integrity", "Value": "sha256-BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk="}]}, {"Route": "images/banners/bxite-F2015051416060001.hw1ap2h7wz.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416060001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "415631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hw1ap2h7wz"}, {"Name": "label", "Value": "images/banners/bxite-F2015051416060001.jpg"}, {"Name": "integrity", "Value": "sha256-3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c="}]}, {"Route": "images/banners/bxite-F2015051416060001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416060001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "415631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c="}]}, {"Route": "images/banners/bxite-F2015051416070001.8co0co73td.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "277459"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8co0co73td"}, {"Name": "label", "Value": "images/banners/bxite-F2015051416070001.jpg"}, {"Name": "integrity", "Value": "sha256-OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U="}]}, {"Route": "images/banners/bxite-F2015051416070001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "277459"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U="}]}, {"Route": "images/banners/bxite-F2015051416070002.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070002.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "185954"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U="}]}, {"Route": "images/banners/bxite-F2015051416070002.t66wtv88gg.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070002.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "185954"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t66wtv88gg"}, {"Name": "label", "Value": "images/banners/bxite-F2015051416070002.jpg"}, {"Name": "integrity", "Value": "sha256-VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U="}]}, {"Route": "images/products/bxite-G2015051717070001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051717070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58073"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI="}]}, {"Route": "images/products/bxite-G2015051717070001.m0xi4xxp31.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051717070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58073"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m0xi4xxp31"}, {"Name": "label", "Value": "images/products/bxite-G2015051717070001.jpg"}, {"Name": "integrity", "Value": "sha256-WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI="}]}, {"Route": "images/products/bxite-G2015051800550001.6i8a2md2c7.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051800550001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6i8a2md2c7"}, {"Name": "label", "Value": "images/products/bxite-G2015051800550001.jpg"}, {"Name": "integrity", "Value": "sha256-cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0="}]}, {"Route": "images/products/bxite-G2015051800550001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051800550001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0="}]}, {"Route": "images/products/bxite-G2015051801210001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801210001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27056"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c="}]}, {"Route": "images/products/bxite-G2015051801210001.tx7hrwy3ub.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801210001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27056"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tx7hrwy3ub"}, {"Name": "label", "Value": "images/products/bxite-G2015051801210001.jpg"}, {"Name": "integrity", "Value": "sha256-cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c="}]}, {"Route": "images/products/bxite-G2015051801300001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801300001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33984"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k="}]}, {"Route": "images/products/bxite-G2015051801300001.mcac68kan4.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801300001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33984"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mcac68kan4"}, {"Name": "label", "Value": "images/products/bxite-G2015051801300001.jpg"}, {"Name": "integrity", "Value": "sha256-ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k="}]}, {"Route": "images/products/bxite-G2015051801390001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801390001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22963"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0="}]}, {"Route": "images/products/bxite-G2015051801390001.tvh7n3sxrd.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801390001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22963"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tvh7n3sxrd"}, {"Name": "label", "Value": "images/products/bxite-G2015051801390001.jpg"}, {"Name": "integrity", "Value": "sha256-SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0="}]}, {"Route": "images/products/bxite-G2015051801470001.1j99jfbvzg.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801470001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15492"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1j99jfbvzg"}, {"Name": "label", "Value": "images/products/bxite-G2015051801470001.jpg"}, {"Name": "integrity", "Value": "sha256-g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4="}]}, {"Route": "images/products/bxite-G2015051801470001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801470001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15492"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4="}]}, {"Route": "images/products/bxite-G2015051801580001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801580001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI="}]}, {"Route": "images/products/bxite-G2015051801580001.xdjj6mqi2s.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051801580001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xdjj6mqi2s"}, {"Name": "label", "Value": "images/products/bxite-G2015051801580001.jpg"}, {"Name": "integrity", "Value": "sha256-5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI="}]}, {"Route": "images/products/bxite-G2015051802100001.e1qt27ox10.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051802100001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25408"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1qt27ox10"}, {"Name": "label", "Value": "images/products/bxite-G2015051802100001.jpg"}, {"Name": "integrity", "Value": "sha256-7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM="}]}, {"Route": "images/products/bxite-G2015051802100001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015051802100001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25408"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM="}]}, {"Route": "images/products/bxite-G2015052301410001.9xalzd3pbe.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301410001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xalzd3pbe"}, {"Name": "label", "Value": "images/products/bxite-G2015052301410001.jpg"}, {"Name": "integrity", "Value": "sha256-cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8="}]}, {"Route": "images/products/bxite-G2015052301410001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301410001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8="}]}, {"Route": "images/products/bxite-G2015052301500001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301500001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE="}]}, {"Route": "images/products/bxite-G2015052301500001.vcnlt9rpyp.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301500001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vcnlt9rpyp"}, {"Name": "label", "Value": "images/products/bxite-G2015052301500001.jpg"}, {"Name": "integrity", "Value": "sha256-gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE="}]}, {"Route": "images/products/bxite-G2015052301590001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301590001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35357"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM="}]}, {"Route": "images/products/bxite-G2015052301590001.u6l54bdsf3.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052301590001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35357"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u6l54bdsf3"}, {"Name": "label", "Value": "images/products/bxite-G2015052301590001.jpg"}, {"Name": "integrity", "Value": "sha256-hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM="}]}, {"Route": "images/products/bxite-G2015052302050001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052302050001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16670"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE="}]}, {"Route": "images/products/bxite-G2015052302050001.tfepd08b58.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052302050001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16670"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfepd08b58"}, {"Name": "label", "Value": "images/products/bxite-G2015052302050001.jpg"}, {"Name": "integrity", "Value": "sha256-p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE="}]}, {"Route": "images/products/bxite-G2015052501290001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052501290001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26366"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI="}]}, {"Route": "images/products/bxite-G2015052501290001.kcc1gx74e1.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\bxite-G2015052501290001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26366"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kcc1gx74e1"}, {"Name": "label", "Value": "images/products/bxite-G2015052501290001.jpg"}, {"Name": "integrity", "Value": "sha256-xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI="}]}, {"Route": "js/carousel.4td0zeg1u6.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1162"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4td0zeg1u6"}, {"Name": "label", "Value": "js/carousel.js"}, {"Name": "integrity", "Value": "sha256-GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs="}]}, {"Route": "js/carousel.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1162"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs="}]}, {"Route": "js/common.dsj294zl38.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\common.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3048"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U4ZP3IWG+V+aKfGyBgAQsGaxIrgUQ5LMpupFVL6E0zw=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dsj294zl38"}, {"Name": "label", "Value": "js/common.js"}, {"Name": "integrity", "Value": "sha256-U4ZP3IWG+V+aKfGyBgAQsGaxIrgUQ5LMpupFVL6E0zw="}]}, {"Route": "js/common.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\common.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3048"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U4ZP3IWG+V+aKfGyBgAQsGaxIrgUQ5LMpupFVL6E0zw=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U4ZP3IWG+V+aKfGyBgAQsGaxIrgUQ5LMpupFVL6E0zw="}]}]}