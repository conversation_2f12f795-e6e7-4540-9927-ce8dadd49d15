@model ShoppingMall.Web.Models.LoginViewModel
@{
    ViewData["Title"] = "登录";
}

<link rel="stylesheet" href="~/css/auth.css" />

<div class="auth-page">
    <div class="auth-container">
        <div class="auth-decoration" style="background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);">
            <div class="decoration-content">
                <div class="decoration-icon">🛍️</div>
                <h1 class="decoration-title">欢迎回来</h1>
                <p class="decoration-subtitle">登录您的账户，开始购物之旅</p>
            </div>
        </div>
        <div class="auth-form-section">
            <div class="auth-header">
                <div class="auth-logo">Shopping Mall</div>
                <h2 class="auth-title">登录</h2>
                <p class="auth-subtitle">请输入您的账户信息</p>
            </div>
            <form asp-action="Login" asp-route-returnurl="@Model.ReturnUrl" method="post" class="auth-form">
                <div asp-validation-summary="All" class="text-danger"></div>
                <div class="form-group">
                    <label asp-for="Email">邮箱</label>
                    <input asp-for="Email" class="form-input" placeholder="请输入邮箱" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Password">密码</label>
                    <div class="password-input-group">
                        <input asp-for="Password" class="form-input" placeholder="请输入密码" />
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>
                <div class="checkbox-group">
                    <input asp-for="RememberMe" type="checkbox" />
                    <label asp-for="RememberMe">记住我</label>
                </div>
                <button type="submit" class="auth-btn">登录</button>
                <div class="auth-links">
                    <a href="#">忘记密码？</a>
                    <span class="auth-divider">|</span>
                    <a asp-action="Register">注册新账户</a>
                </div>
                <div class="social-login">
                    <div class="social-title">其他登录方式</div>
                    <div class="social-buttons">
                        <button type="button" class="social-btn">
                            <span class="fab fa-weixin"></span>
                            微信
                        </button>
                        <button type="button" class="social-btn">
                            <span class="fab fa-qq"></span>
                            QQ
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function togglePassword(button) {
            const input = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
} 