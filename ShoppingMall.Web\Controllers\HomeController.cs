using Microsoft.AspNetCore.Mvc;
using ShoppingMall.Web.Data;
using ShoppingMall.Web.Models;
using Microsoft.EntityFrameworkCore;

namespace ShoppingMall.Web.Controllers;

public class HomeController : Controller
{
    private readonly ApplicationDbContext _context;

    public HomeController(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        var viewModel = new HomeViewModel
        {
            HotProducts = await _context.Products
                .Where(p => p.IsAvailable)
                .OrderByDescending(p => p.CreatedAt)
                .Take(6)
                .ToListAsync(),

            ComputerProducts = await _context.Products
                .Where(p => p.IsAvailable && p.Category == "computer")
                .OrderByDescending(p => p.CreatedAt)
                .Take(2)
                .ToListAsync()
        };

        return View(viewModel);
    }

    public IActionResult Error()
    {
        return View();
    }
} 