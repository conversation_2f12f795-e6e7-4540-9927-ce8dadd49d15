# 商城网站 MVP

这是一个基于ASP.NET Core MVC的简单商城网站MVP版本。

## 功能特性

- 商品管理（添加、查看商品）
- 购物车功能（添加商品到购物车）
- 订单管理
- 用户认证

## 技术栈

- ASP.NET Core 7.0
- Entity Framework Core
- SQL Server
- Bootstrap 5

## 开始使用

### 前置条件

- .NET 7.0 SDK
- SQL Server（LocalDB或完整版）
- Visual Studio 2022或其他支持.NET的IDE

### 安装步骤

1. 克隆仓库到本地：
```bash
git clone [repository-url]
```

2. 进入项目目录：
```bash
cd shopping_mall
```

3. 还原NuGet包：
```bash
dotnet restore
```

4. 更新数据库：
```bash
dotnet ef database update
```

5. 运行项目：
```bash
dotnet run
```

6. 在浏览器中访问：`https://localhost:7001`

## 项目结构

- `Models/` - 数据模型
- `Controllers/` - MVC控制器
- `Views/` - Razor视图
- `Data/` - 数据访问层和DbContext

## 开发计划

- [ ] 完善用户认证和授权
- [ ] 添加购物车功能
- [ ] 实现订单管理
- [ ] 添加支付功能
- [ ] 优化UI/UX
- [ ] 添加商品评论功能
- [ ] 实现商品搜索功能 