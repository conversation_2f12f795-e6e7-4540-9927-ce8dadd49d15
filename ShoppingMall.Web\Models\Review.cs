using System.ComponentModel.DataAnnotations;

namespace ShoppingMall.Web.Models;

public class Review
{
    public int Id { get; set; }
    
    public int ProductId { get; set; }
    public Product Product { get; set; } = null!;
    
    public string UserId { get; set; } = null!;
    public string UserName { get; set; } = null!;
    
    [Required(ErrorMessage = "请输入评分")]
    [Range(1, 5, ErrorMessage = "评分必须在1-5之间")]
    public int Rating { get; set; }
    
    [Required(ErrorMessage = "请输入评价内容")]
    [StringLength(1000, ErrorMessage = "评价内容不能超过1000个字符")]
    public string Content { get; set; } = null!;
    
    public DateTime CreatedAt { get; set; }
    
    public List<string> Images { get; set; } = new();
} 