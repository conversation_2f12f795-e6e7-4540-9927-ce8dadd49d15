/* 商品详情页面布局 */
.product-details {
    padding: 20px 0;
}

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: var(--light-text);
}

.breadcrumb .current {
    color: var(--text-color);
}

/* 商品信息区域 */
.product-info {
    display: grid;
    grid-template-columns: 500px 1fr;
    gap: 40px;
    margin-bottom: 40px;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 商品图片区域 */
.product-gallery {
    position: relative;
}

.main-image {
    position: relative;
    width: 100%;
    height: 500px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.thumbnail-list {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.thumbnails {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.thumbnails::-webkit-scrollbar {
    display: none;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    cursor: pointer;
    object-fit: cover;
    border: 2px solid transparent;
    transition: border-color 0.2s;
}

.thumbnail.active {
    border-color: var(--primary-color);
}

.thumb-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: white;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.thumb-nav.prev {
    left: -15px;
}

.thumb-nav.next {
    right: -15px;
}

/* 商品详细信息 */
.product-details-info {
    padding: 20px 0;
}

.product-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--text-color);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stars {
    color: #f6ad55;
}

.rating-value {
    font-size: 18px;
    font-weight: bold;
}

.review-count {
    color: var(--light-text);
    text-decoration: none;
}

.brand {
    color: var(--light-text);
}

.product-price {
    margin-bottom: 30px;
}

.current-price {
    font-size: 28px;
    color: var(--primary-color);
    font-weight: bold;
}

.original-price {
    font-size: 16px;
    color: var(--light-text);
    text-decoration: line-through;
    margin-left: 10px;
}

.discount {
    display: inline-block;
    margin-left: 10px;
    padding: 2px 6px;
    background: #feebc8;
    color: #c05621;
    border-radius: 4px;
    font-size: 14px;
}

/* 数量选择器 */
.product-options {
    margin-bottom: 30px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 15px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.quantity-controls button {
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: 16px;
}

.quantity-controls input {
    width: 60px;
    height: 36px;
    border: none;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    text-align: center;
    font-size: 16px;
}

.stock {
    color: var(--light-text);
    font-size: 14px;
}

/* 商品操作按钮 */
.product-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.add-to-cart,
.buy-now {
    flex: 1;
    height: 48px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s;
}

.add-to-cart {
    background: var(--primary-color);
    color: white;
}

.add-to-cart:hover {
    background: #c53030;
}

.buy-now {
    background: #38a169;
    color: white;
}

.buy-now:hover {
    background: #2f855a;
}

.add-to-wishlist {
    width: 48px;
    height: 48px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    color: var(--light-text);
    cursor: pointer;
    transition: all 0.2s;
}

.add-to-wishlist:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 服务承诺 */
.product-services {
    display: flex;
    gap: 30px;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
}

.service-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--light-text);
    font-size: 14px;
}

.service-item i {
    color: var(--primary-color);
}

/* 商品详情标签页 */
.product-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 15px 30px;
    border: none;
    background: none;
    color: var(--light-text);
    font-size: 16px;
    cursor: pointer;
    position: relative;
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 商品评价 */
.review-summary {
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.rating-overview {
    display: flex;
    align-items: center;
    gap: 40px;
}

.average-rating {
    text-align: center;
}

.rating-number {
    font-size: 48px;
    color: var(--primary-color);
    font-weight: bold;
    line-height: 1;
    margin-bottom: 10px;
}

.rating-stars {
    color: #f6ad55;
    margin-bottom: 5px;
}

.total-reviews {
    color: var(--light-text);
    font-size: 14px;
}

.review-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-item {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.reviewer-name {
    font-weight: 500;
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #38a169;
    font-size: 12px;
}

.review-rating {
    color: #f6ad55;
}

.review-content {
    margin-bottom: 15px;
    line-height: 1.6;
}

.review-images {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.review-images img {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    object-fit: cover;
    cursor: pointer;
}

.review-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--light-text);
    font-size: 14px;
}

.like-btn {
    background: none;
    border: none;
    color: var(--light-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
}

.like-btn:hover {
    color: var(--primary-color);
}

/* 图片预览模态框 */
.image-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
}

#modalImage {
    max-width: 90%;
    max-height: 90vh;
}

/* 相关商品 */
.related-products {
    margin-bottom: 40px;
}

.related-products h2 {
    font-size: 24px;
    margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .product-info {
        grid-template-columns: 400px 1fr;
    }

    .main-image {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .product-info {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
    }

    .add-to-wishlist {
        width: 100%;
    }

    .rating-overview {
        flex-direction: column;
        align-items: flex-start;
    }
} 