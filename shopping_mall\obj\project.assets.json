{"version": 3, "targets": {"net8.0": {"Microsoft.DotNet.ILCompiler/8.0.15": {"type": "package", "build": {"build/Microsoft.DotNet.ILCompiler.props": {}}}, "Microsoft.NET.ILLink.Tasks/8.0.15": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}}}, "libraries": {"Microsoft.DotNet.ILCompiler/8.0.15": {"sha512": "wMf2N7fJ846aKd73R5gqvtbyqu89/LywlWCtMyXUqKYc9DR3s9kUgNrLIsT9KeRwyinGFJDtRbiib0M4YBX6ZA==", "type": "package", "path": "microsoft.dotnet.ilcompiler/8.0.15", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natstepfilter", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "microsoft.dotnet.ilcompiler.8.0.15.nupkg.sha512", "microsoft.dotnet.ilcompiler.nuspec", "runtime.json", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll", "tools/netstandard/ILCompiler.Build.Tasks.pdb"]}, "Microsoft.NET.ILLink.Tasks/8.0.15": {"sha512": "s4eXlcRGyHeCgFUGQnhq0e/SCHBPp0jOHgMqZg3fQ2OCHJSm1aOUhI6RFWuVIcEb9ig2WgI2kWukk8wu72EbUQ==", "type": "package", "path": "microsoft.net.illink.tasks/8.0.15", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.8.0.15.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net8.0/ILLink.Tasks.deps.json", "tools/net8.0/ILLink.Tasks.dll", "tools/net8.0/Mono.Cecil.Mdb.dll", "tools/net8.0/Mono.Cecil.Pdb.dll", "tools/net8.0/Mono.Cecil.Rocks.dll", "tools/net8.0/Mono.Cecil.dll", "tools/net8.0/Sdk/Sdk.props", "tools/net8.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net8.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net8.0/build/Microsoft.NET.ILLink.targets", "tools/net8.0/illink.deps.json", "tools/net8.0/illink.dll", "tools/net8.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.DotNet.ILCompiler >= 8.0.15", "Microsoft.NET.ILLink.Tasks >= 8.0.15"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\vs assenmbly\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\接单\\C#商城\\shopping_mall\\shopping_mall\\shopping_mall.csproj", "projectName": "shopping_mall", "projectPath": "E:\\接单\\C#商城\\shopping_mall\\shopping_mall\\shopping_mall.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\接单\\C#商城\\shopping_mall\\shopping_mall\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\vs assenmbly\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[8.0.15, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.15, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}