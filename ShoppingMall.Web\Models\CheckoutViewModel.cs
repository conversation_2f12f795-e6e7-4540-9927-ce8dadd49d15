using System.ComponentModel.DataAnnotations;

namespace ShoppingMall.Web.Models;

public class CheckoutViewModel
{
    public List<CartItem> Items { get; set; } = new();

    [Required(ErrorMessage = "请输入收货人姓名")]
    public string ReceiverName { get; set; } = null!;

    [Required(ErrorMessage = "请输入收货人电话")]
    public string ReceiverPhone { get; set; } = null!;

    [Required(ErrorMessage = "请输入收货地址")]
    public string ReceiverAddress { get; set; } = null!;

    public decimal Total => Items.Sum(i => i.SubTotal);
} 